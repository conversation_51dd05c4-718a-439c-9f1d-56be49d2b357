# Toast Migration Testing Guide

This document outlines how to test the migration from snotify to Bootstrap Vue Next's toast system.

## Migration Summary

The toast system has been successfully migrated from vue-snotify to Bootstrap Vue Next with the following changes:

### ✅ Completed Tasks

1. **ToastEventBus Removed**: The old Vue 2 event bus pattern has been eliminated
2. **useToast Composable Created**: A new Vue 3 composable provides the toast functionality
3. **Toast.vue Migrated**: Now uses Bootstrap Vue Next's `BToastOrchestrator` component
4. **ApisCreate.vue Updated**: All `ToastEventBus.$emit` calls replaced with `toast.notify()`
5. **Settings.vue Updated**: All `ToastEventBus.$emit` calls replaced with `toast.notify()`
6. **news.ts Store Updated**: All `ToastEventBus.$emit` calls replaced with direct `useToastController()` usage
7. **Dependencies Cleaned**: No snotify dependencies remain in the codebase

### 🔧 Technical Changes

- **New Composable**: `assets/protected/vue/composables/useToast.ts`
- **Bootstrap Vue Next Native API**: Uses the official Bootstrap Vue Next toast API
- **Toast Types**: Supports success, error, warning, info, and notify
- **Configuration**: Uses Bootstrap Vue Next native options (title, body, variant, value, position, etc.)

## Testing Instructions

### 1. Manual Testing with ToastTest Component

A test component has been created at `assets/protected/vue/component/test/ToastTest.vue` and registered in the app controller.

To use it in your application:

```html
<ToastTest />
```

This component provides buttons to test:
- All toast types (success, error, warning, info, notify)
- Progress bar functionality
- Custom timeouts
- Different positions
- Legacy API compatibility

### 2. Testing in ApisCreate.vue

The main production usage is in the ApisCreate component. Test by:

1. Navigate to any APIS admin section
2. Create or edit an information item
3. Save the item - you should see a success toast
4. Add/remove references - you should see success toasts

### 3. Code Integration Testing

For new components, use the composable:

```vue
<script setup lang="ts">
import { useToast } from '@assets/protected/vue/composables/useToast';

const toast = useToast();

// Show different toast types using Bootstrap Vue Next native API
toast.success({ title: 'Success!', body: 'Operation completed' });
toast.error({ title: 'Error!', body: 'Something went wrong' });
toast.warning({ title: 'Warning!', body: 'Please be careful' });
toast.info({ title: 'Info', body: 'Here is some information' });
toast.notify({ title: 'Notification', body: 'General notification' });
</script>
```

### 4. Configuration Options

Bootstrap Vue Next native configuration options:

```typescript
toast.success({
  title: 'Custom Toast',
  body: 'Message content',
  value: 5000,                    // Auto-dismiss after 5 seconds
  progressProps: { variant: 'primary' }, // Show progress bar
  position: 'top-center',         // Position on screen
  solid: true,                    // Solid background
  noCloseButton: false,           // Show close button
});
```

### 5. Expected Behavior

- **Visual**: Toasts should appear with Bootstrap 5 styling
- **Position**: Default bottom-right, customizable
- **Auto-dismiss**: Configurable timeout (default 2 seconds)
- **Progress Bar**: Optional, shows countdown when enabled
- **Variants**: Different colors for different toast types
- **Accessibility**: Proper ARIA attributes for screen readers

## Troubleshooting

### Common Issues

1. **Toasts not appearing**: Ensure `BToastOrchestrator` is included in your app
2. **Styling issues**: Verify Bootstrap Vue Next CSS is loaded
3. **TypeScript errors**: Check import paths for the useToast composable

### Verification Checklist

- [ ] Success toasts appear with green styling
- [ ] Error toasts appear with red styling  
- [ ] Warning toasts appear with yellow styling
- [ ] Info/notify toasts appear with blue styling
- [ ] Progress bars work when enabled
- [ ] Custom timeouts are respected
- [ ] Different positions work correctly
- [ ] Toasts auto-dismiss after specified time
- [ ] No console errors related to toast functionality

## Migration Benefits

1. **Vue 3 Compatibility**: Uses modern Vue 3 patterns
2. **Better Performance**: No event bus overhead
3. **Type Safety**: Full TypeScript support
4. **Maintainability**: Cleaner, more readable code
5. **Bootstrap Integration**: Consistent with existing UI framework
6. **Accessibility**: Better screen reader support

## Next Steps

After testing confirms everything works correctly:

1. Remove the test component if not needed in production
2. Consider adding more toast types if needed
3. Update documentation for other developers
4. Monitor for any issues in production usage

The migration maintains full backward compatibility while providing a modern, Vue 3-compatible toast system.

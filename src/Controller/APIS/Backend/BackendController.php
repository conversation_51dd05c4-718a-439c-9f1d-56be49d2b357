<?php
/**
 * <PERSON><PERSON><PERSON>: <PERSON>
 * E-Mail: <EMAIL>
 * Datum: 21.12.17 14:46.
 */

namespace App\Controller\APIS\Backend;

use App\Model\Api\PortalUser\PortalUserInterface;
use App\Model\APIS\Configuration\ConfigurationGetterYML;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/apisadmin')]
class BackendController extends AbstractController
{
    /**
     * @throws \App\Exception\APIS\ConfigurationGetterException
     */
    #[Route('/{section}', name: 'apis_backend_index', requirements: ['section' => 'sales|mounting|service|technical'], methods: ['GET'])]
    public function index(PortalUserInterface $portalUser, ConfigurationGetterYML $configurationGetterYML, string $section): Response
    {
        $portalUser->fetch();

        // TODO kann ggf weg
        // Kategorien laden und sortieren
        $configurationObject = $configurationGetterYML->load($section);
        $categories = $configurationObject->categories;
        sort($categories);

        // TODO kann ggf weg
        $possibleReceivers = [];
        foreach ($configurationObject->roles->user as $role) {
            $possibleReceivers[$role->getName()] = $role->getName();
        }

        return $this->render(
            'APIS/Backend/index.html.twig',
            [
                'categories' => $categories,
                'receivers' => $possibleReceivers,
                'user' => $portalUser,
                'openModule' => 'information',
                'openSubmodule' => $section,
                'module' => $section,
            ]
        );
    }

    /**
     * @throws \App\Exception\APIS\ConfigurationGetterException
     */
    #[Route('/create/{section}', name: 'apis_backend_create', requirements: ['section' => 'sales|mounting|service|technical'], methods: ['GET'])]
    #[Route('/edit/{section}/{number}', name: 'apis_backend_edit', requirements: ['section' => 'sales|mounting|service|technical', 'number' => '\d+'], methods: ['GET'])]
    public function edit(PortalUserInterface $portalUser, ConfigurationGetterYML $configurationGetterYML, string $section, int $number = -1): Response
    {
        $portalUser->fetch();

        // Kategorien laden und sortieren
        $configurationObject = $configurationGetterYML->load($section);
        $categories = $configurationObject->categories;
        sort($categories);

        $possibleReceivers = [];
        foreach ($configurationObject->roles->user as $role) {
            $possibleReceivers[$role->getName()] = $role->getName();
        }

        return $this->render(
            'APIS/Backend/editCreate.html.twig',
            [
                'categories' => $categories,
                'receivers' => $possibleReceivers,
                'number' => $number,
                'sectionprefix' => $configurationObject->section->getPrefix(),
                'user' => $portalUser,
                'openModule' => 'information',
                'openSubmodule' => $section,
                'module' => $section,
            ]
        );
    }
}

div#tdata {

  .card {
    &.card-closed a.card-minimize {
      svg {
        transform: rotate(180deg);
      }
    }
  }

  .card-header.productBox {
    div.image {
      float: left;
      width: 100px;
      margin-right: 20px;

      img {
        height: 30px;
      }
    }

    div.product {
      font-size: 18px;
      line-height: 30px;
      color: #333333;
    }

    div.card-controls {
      top: 5px;
    }

    &:hover {
      text-decoration: none;
    }
  }

  div.infobox {
    font-size: 0.9em;
    color: #686868;
  }


  div.product {
    width: 100%;

    div.bar {
      background-color: #8a8a8a;
      color: #ffffff;
      clear: both;
      float: left;
      margin-right: 20px;
      padding: 3px;
      margin-bottom: 5px;
    }

    div.second {
      padding: 3px;
      margin-bottom: 5px;
    }
  }

  table.resultTable {
    font-size: 0.9rem;
  }

  div.tdataProductsWrapper {
    overflow: auto;
    max-height: 80vh;
  }

  option:disabled {
    background: #ffe2e2;
  }
}


<template>
  <div class="toast-test-container p-4">
    <h2>Toast Migration Test</h2>
    <p>Test all toast types to ensure the migration from snotify to Bootstrap Vue Next is working correctly.</p>
    
    <div class="row">
      <div class="col-md-6">
        <h3>Basic Toast Types</h3>
        <div class="d-grid gap-2">
          <BButton variant="success" @click="testSuccess">Test Success Toast</BButton>
          <BButton variant="danger" @click="testError">Test Error Toast</BButton>
          <BButton variant="warning" @click="testWarning">Test Warning Toast</BButton>
          <BButton variant="info" @click="testInfo">Test Info Toast</BButton>
          <BButton variant="primary" @click="testNotify">Test Notify Toast</BButton>
        </div>
      </div>
      
      <div class="col-md-6">
        <h3>Advanced Options</h3>
        <div class="d-grid gap-2">
          <BButton variant="outline-success" @click="testWithProgress">Toast with Progress Bar</BButton>
          <BButton variant="outline-info" @click="testLongTimeout">Toast with Long Timeout (10s)</BButton>
          <BButton variant="outline-warning" @click="testCustomPosition">Toast at Top Center</BButton>
          <BButton variant="outline-primary" @click="testLegacyAPI">Test Legacy API (like ApisCreate)</BButton>
        </div>
      </div>
    </div>
    
    <div class="mt-4">
      <h3>Migration Status</h3>
      <ul class="list-group">
        <li class="list-group-item d-flex justify-content-between align-items-center">
          ✅ ToastEventBus removed
          <span class="badge bg-success rounded-pill">Complete</span>
        </li>
        <li class="list-group-item d-flex justify-content-between align-items-center">
          ✅ useToast composable created
          <span class="badge bg-success rounded-pill">Complete</span>
        </li>
        <li class="list-group-item d-flex justify-content-between align-items-center">
          ✅ Toast.vue migrated to Bootstrap Vue Next
          <span class="badge bg-success rounded-pill">Complete</span>
        </li>
        <li class="list-group-item d-flex justify-content-between align-items-center">
          ✅ ApisCreate.vue updated
          <span class="badge bg-success rounded-pill">Complete</span>
        </li>
        <li class="list-group-item d-flex justify-content-between align-items-center">
          ✅ Settings.vue updated
          <span class="badge bg-success rounded-pill">Complete</span>
        </li>
        <li class="list-group-item d-flex justify-content-between align-items-center">
          ✅ news.ts store updated
          <span class="badge bg-success rounded-pill">Complete</span>
        </li>
        <li class="list-group-item d-flex justify-content-between align-items-center">
          ✅ Snotify dependencies removed
          <span class="badge bg-success rounded-pill">Complete</span>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useToast } from '@assets/protected/vue/composables/useToast';

const toast = useToast();

// Basic toast type tests
const testSuccess = () => {
  toast.success({
    title: 'Success!',
    text: 'This is a success toast message.',
  });
};

const testError = () => {
  toast.error({
    title: 'Error!',
    text: 'This is an error toast message.',
  });
};

const testWarning = () => {
  toast.warning({
    title: 'Warning!',
    text: 'This is a warning toast message.',
  });
};

const testInfo = () => {
  toast.info({
    title: 'Information',
    text: 'This is an info toast message.',
  });
};

const testNotify = () => {
  toast.notify({
    title: 'Notification',
    text: 'This is a notify toast message (same as info).',
  });
};

// Advanced option tests
const testWithProgress = () => {
  toast.success({
    title: 'With Progress',
    text: 'This toast has a progress bar.',
    progressBar: true,
    timeout: 5000,
  });
};

const testLongTimeout = () => {
  toast.info({
    title: 'Long Timeout',
    text: 'This toast will stay visible for 10 seconds.',
    timeout: 10000,
  });
};

const testCustomPosition = () => {
  toast.warning({
    title: 'Custom Position',
    text: 'This toast appears at the top center.',
    position: 'top-center',
  });
};

// Test the legacy API format used by ApisCreate.vue
const testLegacyAPI = () => {
  toast.notify({
    title: 'Legacy API Test',
    text: 'This simulates the format used in ApisCreate.vue',
  });
};
</script>

<style scoped>
.toast-test-container {
  max-width: 800px;
  margin: 0 auto;
}
</style>

<template>
  <vue-snotify :class="style"></vue-snotify>
</template>

<script setup lang="ts">
import { ref, onMounted, getCurrentInstance } from 'vue';
import Vue from 'vue';

import Snotify, { SnotifyPosition } from 'vue-snotify';
import 'vue-snotify/styles/material.scss';

import ToastEventBus from '@assets/protected/vue/eventBuses/toast';

Vue.use(Snotify);

/*
 * Default Settings
 */
const style = ref<string>('material');
const timeout = ref<number>(2000);
const position = ref<SnotifyPosition>(SnotifyPosition.rightBottom);
const progressBar = ref<boolean>(false);
const closeClick = ref<boolean>(true);
const newTop = ref<boolean>(true);
const backdrop = ref<number>(-1);
const dockMax = ref<number>(8);
const blockMax = ref<number>(6);
const pauseHover = ref<boolean>(true);
const titleMaxLength = ref<number>(15);
const bodyMaxLength = ref<number>(80);

const { proxy } = getCurrentInstance()!;

/*
 * Configuration Object zusammenstellen
 */
const getConfig = () => {
  (proxy as any).$snotify.setDefaults({
    global: {
      newOnTop: newTop.value,
      maxAtPosition: blockMax.value,
      maxOnScreen: dockMax.value,
    },
  });
  return {
    bodyMaxLength: bodyMaxLength.value,
    titleMaxLength: titleMaxLength.value,
    backdrop: backdrop.value,
    position: position.value,
    timeout: timeout.value,
    showProgressBar: progressBar.value,
    closeOnClick: closeClick.value,
    pauseOnHover: pauseHover.value,
  };
};

/*
 * Toast anzeigen
 */
const showToast = (type: string, config: any): void => {
  if (config.timeout !== undefined) timeout.value = config.timeout;
  if (config.progressBar !== undefined) progressBar.value = config.progressBar;
  if (type === 'notify') (proxy as any).$snotify.info(config.text, config.title, getConfig());
};

/*
 * Events
 */
onMounted(() => {
  ToastEventBus.$on('notify', (config: any) => {
    showToast('notify', config);
  });
});
</script>

<template>
  <BToastOrchestrator />
</template>

<script setup lang="ts">
import { ref, provide } from 'vue';
import { useToast, type ToastConfig } from '@assets/protected/vue/composables/useToast';

const timeout = ref<number>(2000);
const position = ref<string>('bottom-right');
const progressBar = ref<boolean>(false);

const toast = useToast();

const getConfig = (): ToastConfig => {
  return {
    timeout: timeout.value,
    position: position.value as any,
    progressBar: progressBar.value,
  };
};

const showToast = (type: string, config: any): void => {
  if (config.timeout !== undefined) timeout.value = config.timeout;
  if (config.progressBar !== undefined) progressBar.value = config.progressBar;

  const toastConfig: ToastConfig = {
    ...getConfig(),
    title: config.title,
    text: config.text,
    body: config.body,
    timeout: config.timeout || timeout.value,
    progressBar: config.progressBar !== undefined ? config.progressBar : progressBar.value,
  };

  switch (type) {
    case 'success':
      toast.success(toastConfig);
      break;
    case 'error':
      toast.error(toastConfig);
      break;
    case 'warning':
      toast.warning(toastConfig);
      break;
    case 'info':
      toast.info(toastConfig);
      break;
    case 'notify':
    default:
      toast.notify(toastConfig);
      break;
  }
};

/*
 * Provide toast functionality for child components
 */
provide('toast', {
  showToast,
  success: (config: any) => showToast('success', config),
  error: (config: any) => showToast('error', config),
  warning: (config: any) => showToast('warning', config),
  info: (config: any) => showToast('info', config),
  notify: (config: any) => showToast('notify', config),
});
</script>

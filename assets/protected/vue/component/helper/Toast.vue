<template>
  <!-- Bootstrap Vue Next Toast Orchestrator -->
  <BToastOrchestrator />
</template>

<script setup lang="ts">
import { provide } from 'vue';
import { useToast, type ToastOptions } from '@assets/protected/vue/composables/useToast';

const toast = useToast();

/**
 * Provide toast functionality for child components using Bootstrap Vue Next native API
 */
provide('toast', {
  show: (options: ToastOptions) => toast.show(options),
  success: (options: Omit<ToastOptions, 'variant'>) => toast.success(options),
  error: (options: Omit<ToastOptions, 'variant'>) => toast.error(options),
  warning: (options: Omit<ToastOptions, 'variant'>) => toast.warning(options),
  info: (options: Omit<ToastOptions, 'variant'>) => toast.info(options),
  notify: (options: Omit<ToastOptions, 'variant'>) => toast.notify(options),
});
</script>

<template>
  <!-- Bootstrap Vue Next Toast Orchestrator -->
  <BToastOrchestrator />
</template>

<script setup lang="ts">
import { ref, onMounted, provide, getCurrentInstance } from 'vue';
import { useToast, type ToastConfig } from '@assets/protected/vue/composables/useToast';

/*
 * Default Settings - maintained for compatibility
 */
const timeout = ref<number>(2000);
const position = ref<string>('bottom-right');
const progressBar = ref<boolean>(false);
const closeClick = ref<boolean>(true);
const newTop = ref<boolean>(true);
const backdrop = ref<number>(-1);
const dockMax = ref<number>(8);
const blockMax = ref<number>(6);
const pauseHover = ref<boolean>(true);
const titleMaxLength = ref<number>(15);
const bodyMaxLength = ref<number>(80);

const { proxy } = getCurrentInstance()!;
const toast = useToast();

/*
 * Configuration Object zusammenstellen
 */
const getConfig = (): ToastConfig => {
  return {
    timeout: timeout.value,
    position: position.value as any,
    progressBar: progressBar.value,
  };
};

/*
 * Toast anzeigen
 */
const showToast = (type: string, config: any): void => {
  if (config.timeout !== undefined) timeout.value = config.timeout;
  if (config.progressBar !== undefined) progressBar.value = config.progressBar;

  const toastConfig: ToastConfig = {
    ...getConfig(),
    title: config.title,
    text: config.text,
    body: config.body,
    timeout: config.timeout || timeout.value,
    progressBar: config.progressBar !== undefined ? config.progressBar : progressBar.value,
  };

  // Use the appropriate toast method based on type
  switch (type) {
    case 'success':
      toast.success(toastConfig);
      break;
    case 'error':
      toast.error(toastConfig);
      break;
    case 'warning':
      toast.warning(toastConfig);
      break;
    case 'info':
      toast.info(toastConfig);
      break;
    case 'notify':
    default:
      toast.notify(toastConfig);
      break;
  }
};

/*
 * Provide toast functionality for child components
 */
provide('toast', {
  showToast,
  success: (config: any) => showToast('success', config),
  error: (config: any) => showToast('error', config),
  warning: (config: any) => showToast('warning', config),
  info: (config: any) => showToast('info', config),
  notify: (config: any) => showToast('notify', config),
});

/*
 * Events - Legacy event bus support (will be removed after migration)
 */
onMounted(() => {
  // For now, we'll keep this as a placeholder
  // This will be removed once all components are migrated to use the composable
});
</script>

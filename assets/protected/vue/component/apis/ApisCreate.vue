<template>
  <div v-cloak>
    <Card
      v-if="$number < 0 && (newNumber > 0 || newNumber === '')"
      card-class="header-bg-warning header-color-white mt20"
      :canToggle="false">
      <template v-slot:title>{{ t('apis/admin/chooseNumber') }}</template>
      <template v-slot:body>
        <div class="row">
          <div class="col-md-3">
            <div class="input-group">
              <div class="input-group-prepend">
                <div class="input-group-text" v-html="sectionprefix"></div>
              </div>
              <input v-model="newNumber" class="form-control" type="text" />
            </div>
          </div>
          <div class="col-md-3">
            <button class="btn btn-primary" @click="createNew()" v-html="t('apis/admin/createInformation')"></button>
          </div>
        </div>
      </template>
    </Card>

    <div v-if="$number > 0 && dataLoaded">
      <Card card-class="header-bg-abus-blue-dark header-color-white mt20" :canToggle="true">
        <template v-slot:title> <img class="flag" :src="flagDe" /> {{ t('apis/german') }} </template>
        <template v-slot:body>
          <ApisCreateBlock
            id="ceateBlockGerman"
            :number="$number"
            :section="section"
            :sectionprefix="sectionprefix"
            lang="ger"
            :title="contentBlocks.ger_title"
            :content="contentBlocks.ger_content"
            :attachments="attachmentsGer" />
        </template>
      </Card>

      <Card card-class="header-bg-abus-blue-dark header-color-white mt20" :canToggle="true" :isClosed="cardEngClosed">
        <template v-slot:title>
          <div style="float: left; width: 120px">
            <img class="flag" :src="flagGb" />
            {{ t('apis/english') }}
          </div>
          <div style="float: left; margin-left: 50px" @click="setTranslation($event, 'eng_translate')">
            <div class="custom-control custom-checkbox">
              <input
                id="checkboxTranslateEnglish"
                class="custom-control-input"
                type="checkbox"
                value="true"
                :checked="contentBlocks.eng_translate" />
              <label
                class="custom-control-label"
                for="checkboxTranslateEnglish"
                v-html="t('apis/admin/translate')"></label>
            </div>
          </div>
        </template>
        <template v-slot:body>
          <ApisCreateBlock
            id="ceateBlockEnglish"
            :number="$number"
            :section="section"
            :sectionprefix="sectionprefix"
            lang="eng"
            :title="contentBlocks.eng_title"
            :content="contentBlocks.eng_content"
            :attachments="attachmentsEng" />
        </template>
      </Card>

      <Card card-class="header-bg-abus-blue-dark header-color-white mt20" :canToggle="true" :isClosed="cardFreClosed">
        <template v-slot:title>
          <div style="float: left; width: 120px">
            <img class="flag" :src="flagFr" />
            {{ t('apis/french') }}
          </div>
          <div style="float: left; margin-left: 50px" @click="setTranslation($event, 'fre_translate')">
            <div class="custom-control custom-checkbox">
              <input
                id="checkboxTranslateFrench"
                class="custom-control-input"
                type="checkbox"
                :value="true"
                :checked="contentBlocks.fre_translate" />
              <label
                class="custom-control-label"
                for="checkboxTranslateFrench"
                v-html="t('apis/admin/translate')"></label>
            </div>
          </div>
        </template>
        <template v-slot:body>
          <ApisCreateBlock
            id="ceateBlockFrench"
            :number="$number"
            :section="section"
            :sectionprefix="sectionprefix"
            lang="fre"
            :title="contentBlocks.fre_title"
            :content="contentBlocks.fre_content"
            :attachments="attachmentsFre" />
        </template>
      </Card>

      <Card card-class="header-bg-abus-blue-dark header-color-white mt20" :canToggle="true" :isClosed="cardEslClosed">
        <template v-slot:title>
          <div style="float: left; width: 120px">
            <img class="flag" :src="flagEs" />
            {{ t('apis/spanish') }}
          </div>
          <div style="float: left; margin-left: 50px" @click="setTranslation($event, 'esl_translate')">
            <div class="custom-control custom-checkbox">
              <input
                id="checkboxTranslateSpanish"
                class="custom-control-input"
                type="checkbox"
                :value="true"
                :checked="contentBlocks.esl_translate" />
              <label
                class="custom-control-label"
                for="checkboxTranslateSpanish"
                v-html="t('apis/admin/translate')"></label>
            </div>
          </div>
        </template>
        <template v-slot:body>
          <ApisCreateBlock
            id="ceateBlockSpanish"
            :number="$number"
            :section="section"
            :sectionprefix="sectionprefix"
            lang="esl"
            :title="contentBlocks.esl_title"
            :content="contentBlocks.esl_content"
            :attachments="attachmentsEsl" />
        </template>
      </Card>

      <Card card-class="header-bg-primary header-color-white mt20">
        <template v-slot:title>{{ t('apis/admin/references') }}</template>
        <template v-slot:body>
          <div class="row">
            <div class="col-md-6">
              <TypeaheadInput
                :src="'/apisadmin/reference/search/' + section"
                query-param-name="search"
                :placeholder="t('apis/admin/searchReference')"
                @hit="onSelectReference" />
            </div>
          </div>

          <div v-for="(reference, key, index) in references" class="referenceContainer">
            <hr />

            <div class="row mb10">
              <div v-if="reference.replaces" class="col-md-12">
                <div class="alert alert-danger pl10" v-html="t('apis/admin/replacementWarning')"></div>
              </div>

              <div class="col-md-6">
                <input
                  class="form-control"
                  type="text"
                  :value="`${sectionprefix} - ${reference.number} ${reference.name}`"
                  disabled />
              </div>

              <div class="col-md-4">
                <div class="checkboxContainer">
                  <div class="custom-control custom-checkbox">
                    <input
                      :id="'replacement_' + index"
                      v-model="reference.replaces"
                      class="custom-control-input"
                      type="checkbox"
                      value="true"
                      @change="saveReferences(key)" />
                    <label
                      class="custom-control-label"
                      :for="'replacement_' + index"
                      v-html="t('apis/admin/replacement')"></label>
                  </div>
                </div>
              </div>

              <div class="col-md-2" style="text-align: right">
                <button class="btn btn-danger btn-md" @click="removeReference(reference.number)">
                  <i class="fas fa-trash-alt" aria-hidden="true"></i>
                  {{ t('apis/admin/deleteReference') }}
                </button>
              </div>
            </div>

            <div class="row mb10">
              <div class="col-md-3">
                <div class="input-group">
                  <div class="input-group-prepend">
                    <div class="input-group-text"><img src="@assets/public/images/flags/de.png" /></div>
                  </div>
                  <input
                    id="referenceReasonGer"
                    v-model="reference.reason_ger"
                    class="form-control"
                    type="text"
                    :placeholder="t('apis/admin/reason')"
                    @blur="saveReferences(key)" />
                </div>
              </div>

              <div class="col-md-3">
                <div class="input-group">
                  <div class="input-group-prepend">
                    <div class="input-group-text"><img src="@assets/public/images/flags/gb.png" /></div>
                  </div>
                  <input
                    id="referenceReasonEngr"
                    v-model="reference.reason_eng"
                    class="form-control"
                    type="text"
                    :placeholder="t('apis/admin/reason')"
                    @blur="saveReferences(key)" />
                </div>
              </div>

              <div class="col-md-3">
                <div class="input-group">
                  <div class="input-group-prepend">
                    <div class="input-group-text"><img src="@assets/public/images/flags/fr.png" /></div>
                  </div>
                  <input
                    id="referenceReasonFre"
                    v-model="reference.reason_fre"
                    class="form-control"
                    type="text"
                    :placeholder="t('apis/admin/reason')"
                    @blur="saveReferences(key)" />
                </div>
              </div>

              <div class="col-md-3">
                <div class="input-group">
                  <div class="input-group-prepend">
                    <div class="input-group-text"><img src="@assets/public/images/flags/es.png" /></div>
                  </div>
                  <input
                    id="referenceReasonEsl"
                    v-model="reference.reason_esl"
                    class="form-control"
                    type="text"
                    :placeholder="t('apis/admin/reason')"
                    @blur="saveReferences(key)" />
                </div>
              </div>
            </div>
          </div>
        </template>
      </Card>

      <Card card-class="header-bg-primary header-color-white mt20">
        <template v-slot:title>{{ t('apis/admin/categories') }}</template>
        <template v-slot:body>
          <div class="row">
            <div v-for="(category, key, index) in categories" class="col-md-3">
              <div class="custom-control custom-checkbox mb5">
                <input
                  :id="'checkbox_categories_' + index"
                  v-model="selectedCategories[key]"
                  class="custom-control-input"
                  type="checkbox"
                  :value="category" />
                <label class="custom-control-label" :for="'checkbox_categories_' + index">
                  <span class="badge badge-default" v-html="category"></span>
                </label>
              </div>
            </div>
          </div>
        </template>
      </Card>

      <Card
        v-if="informationBuilder?.information?.workflow !== 'released'"
        card-class="header-bg-primary header-color-white mt20">
        <template v-slot:title>{{ t('apis/admin/receivers') }}</template>
        <template v-slot:body>
          <div class="row">
            <div v-for="(receiver, key, index) in receivers" class="col-md-3">
              <div class="custom-control custom-checkbox mb5">
                <input
                  :id="'checkbox_receivers_' + index"
                  v-model="selectedReceivers[key]"
                  class="custom-control-input"
                  type="checkbox"
                  :value="true" />
                <label
                  class="custom-control-label"
                  :for="'checkbox_receivers_' + index"
                  v-html="t('apis/admin/' + receiver.toUpperCase())"></label>
              </div>
            </div>
          </div>
          <div class="row mt10">
            <div class="col-md-3">
              <button class="btn btn-warning" @click="showReceiverList()">
                <i class="fa fa-list" style="margin-right: 10px"></i>{{ t('apis/admin/receiverList') }}
              </button>
            </div>
          </div>
        </template>
      </Card>

      <Card card-class="header-bg-primary header-color-white mt20">
        <template v-slot:title>{{ t('apis/admin/author') }}</template>
        <template v-slot:body>
          <div class="row">
            <div class="col-md-4">
              <div class="input-group">
                <div class="input-group-prepend">
                  <div class="input-group-text">
                    <i class="fa fa-user"></i>
                  </div>
                </div>
                <input
                  v-model="author"
                  class="form-control"
                  type="text"
                  :placeholder="t('apis/admin/authorName')"
                  @blur="update()" />
              </div>
            </div>
            <div class="col-md-4">
              <div class="input-group">
                <div class="input-group-prepend">
                  <div class="input-group-text">
                    <i class="fa fa-envelope"></i>
                  </div>
                </div>
                <input
                  v-model="authorEmail"
                  class="form-control"
                  type="text"
                  :placeholder="t('apis/admin/authorMail')"
                  @blur="update()" />
              </div>
            </div>
            <div class="col-md-4">
              <div class="input-group">
                <div class="input-group-prepend">
                  <div class="input-group-text">
                    <i class="fa fa-phone"></i>
                  </div>
                </div>
                <input
                  v-model="authorPhone"
                  class="form-control"
                  type="text"
                  :placeholder="t('apis/admin/authorPhone')"
                  @blur="update()" />
              </div>
            </div>
          </div>
        </template>
      </Card>

      <Card
        v-if="informationBuilder?.information?.workflow !== 'released'"
        card-class="header-bg-primary header-color-white mt20">
        <template v-slot:title>{{ t('apis/admin/publishing') }}</template>
        <template v-slot:body>
          <div class="row">
            <div class="col-md-4">
              <div class="input-group" style="color: #000000">
                <div class="input-group-prepend">
                  <div class="input-group-text">
                    <FontAwesomeIcon fixed-width icon="calendar" />
                  </div>
                </div>
                <!-- TODO: Replace date-picker component -->
                <!--                <date-picker
                  v-model="releaseDate"
                  :config="config"
                  :wrap="true"
                  :placeholder="t('apis/admin/releaseDate')"></date-picker>-->
              </div>
            </div>
            <div class="col-md-6">
              <div class="custom-control custom-checkbox mt10">
                <input
                  id="sendMailInput"
                  v-model="sendMail"
                  class="custom-control-input"
                  type="checkbox"
                  :value="true"
                  @change="update()" />
                <label class="custom-control-label" for="sendMailInput" v-html="t('apis/admin/sendMail')"></label>
              </div>
            </div>
          </div>
        </template>
      </Card>

      <Card card-class="header-bg-warning header-color-white mt20">
        <template v-slot:title>{{ t('apis/admin/memo') }}</template>
        <template v-slot:body>
          <div class="row">
            <div class="col-md-12">
              <textarea
                v-model="memo"
                class="form-control limitTextarea"
                maxlength="2000"
                rows="3"
                @blur="update()"></textarea>
            </div>
          </div>
        </template>
      </Card>

      <div v-if="canTransit() === false" class="bs-callout bs-callout-danger">
        <h4 v-html="t('apis/admin/requirements')"></h4>
        <p v-html="t('apis/admin/requirements_description')"></p>
        <ul>
          <li
            v-if="contentBlocks.ger_title === undefined || contentBlocks.ger_title === ''"
            v-html="t('apis/admin/requirement_title')"></li>
          <li
            v-if="contentBlocks.ger_content === undefined || contentBlocks.ger_content.replace(/<.+?>/g, '') === ''"
            v-html="t('apis/admin/requirement_text')"></li>
          <li v-if="categoriesCount() === 0" v-html="t('apis/admin/requirement_category')"></li>
          <li v-if="receiversCount() === 0" v-html="t('apis/admin/requirement_receiver')"></li>
          <li
            v-if="author === undefined || author === null || author === ''"
            v-html="t('apis/admin/requirement_author_name')"></li>
          <li
            v-if="authorEmail === undefined || authorEmail === null || authorEmail === ''"
            v-html="t('apis/admin/requirement_author_mail')"></li>
          <li
            v-if="authorPhone === undefined || authorPhone === null || authorPhone === ''"
            v-html="t('apis/admin/requirement_author_phone')"></li>
          <li
            v-if="releaseDate === '' || releaseDate === null || releaseDate === undefined"
            v-html="t('apis/admin/requirement_release_date')"></li>
        </ul>
      </div>

      <div class="row store">
        <div class="col-md-8"></div>
        <div class="col-md-2">
          <button class="btn btn-warning" @click="update()">
            <FontAwesomeIcon class="mr5" :icon="['far', 'hdd']" /> {{ t('apis/admin/store') }}
          </button>
        </div>
        <div class="col-md-2">
          <button class="btn btn-success" @click="update(true)">
            <FontAwesomeIcon class="mr5" icon="sign-out" /> {{ t('apis/admin/storeAndClose') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted, getCurrentInstance } from 'vue';
import axios from 'axios';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import { useDateHelpers } from '@assets/protected/vue/composables/useDateHelpers';

import flagDe from '@flags/png100px/de.png';
import flagGb from '@flags/png100px/gb.png';
import flagFr from '@flags/png100px/fr.png';
import flagEs from '@flags/png100px/es.png';

import APISEventBus from 'EventBuses/apis';
import ModalEventBus from 'EventBuses/modal';
import { useToast } from '@assets/protected/vue/composables/useToast';

import Card from '@assets/protected/vue/component/helper/Card.vue';
import TypeaheadInput from '@assets/protected/vue/component/helper/TypeaheadInput.vue';
import ApisCreateBlock from '@assets/protected/vue/component/apis/ApisCreateBlock.vue';

const { formatDate } = useDateHelpers();
const toast = useToast();

const { proxy } = getCurrentInstance()!;
const t = proxy.$t;

interface Props {
  categories: object;
  receivers: object;
  section: string;
  sectionprefix: string;
  number: number;
}

const props = defineProps<Props>();

const $number = ref<number>(-1);
const newNumber = ref<number>(0);

const dataLoaded = ref<boolean>(false);
const informationBuilder = ref<any>(null);

const contentBlocks = ref({
  ger_title: '',
  ger_content: '',
  eng_title: '',
  eng_content: '',
  eng_translate: false,
  fre_title: '',
  fre_content: '',
  fre_translate: false,
  esl_title: '',
  esl_content: '',
  esl_translate: false,
});

const attachmentsGer = ref<any>({});
const attachmentsEng = ref<any>({});
const attachmentsFre = ref<any>({});
const attachmentsEsl = ref<any>({});

const cardEngClosed = ref<boolean>(true);
const cardFreClosed = ref<boolean>(true);
const cardEslClosed = ref<boolean>(true);

const references = ref<any>({});

const selectedCategories = ref<any>({ disableWatchWhileLoading: true });
const selectedReceivers = ref<any>({ disableWatchWhileLoading: true });

const author = ref<string>('');
const authorEmail = ref<string>('');
const authorPhone = ref<string>('');

const releaseDate = ref<string>('disableWatchWhileLoading');
const releaseDateNormalized = ref<string>('');

const sendMail = ref<boolean>(true);
const memo = ref<string>('');
const workflow = ref<string>('');

watch(
  selectedCategories,
  (value: any, oldValue: any) => {
    if (oldValue.disableWatchWhileLoading === undefined) {
      update();
    }
  },
  { deep: true },
);

watch(
  selectedReceivers,
  (value: any, oldValue: any) => {
    if (oldValue.disableWatchWhileLoading === undefined) {
      update();
    }
  },
  { deep: true },
);

watch(releaseDate, (value: any, oldValue: any) => {
  if (oldValue !== 'disableWatchWhileLoading') {
    releaseDateNormalized.value = formatDate(releaseDate.value);
    update();
  }
});

// Helper function to serialize form data (replacing qs library)
const serializeFormData = (data: any): string => {
  const params = new URLSearchParams();
  for (const key in data) {
    if (data.hasOwnProperty(key)) {
      if (typeof data[key] === 'object' && data[key] !== null) {
        params.append(key, JSON.stringify(data[key]));
      } else {
        params.append(key, data[key]);
      }
    }
  }
  return params.toString();
};

/**
 * Speichert Änderungen an der Information
 */
const update = (close: boolean = false): void => {
  const url = '/apisadmin/save/' + props.section + '/' + $number.value;

  const baseParams = {
    author: author.value,
    authorEmail: authorEmail.value,
    authorPhone: authorPhone.value,
    releaseDate: releaseDateNormalized.value,
    sendMail: String(sendMail.value),
    memo: memo.value ? memo.value : '',
  };

  const categories = Object.entries(selectedCategories.value).reduce(
    (acc, [key, value]) => {
      acc[`categories[${key}]`] = value;
      return acc;
    },
    {} as Record<string, string>,
  );

  const receivers = Object.entries(selectedReceivers.value).reduce(
    (acc, [key, value]) => {
      acc[`receivers[${key}]`] = value;
      return acc;
    },
    {} as Record<string, string>,
  );

  const content = Object.fromEntries(
    Object.entries(contentBlocks.value).filter(([_, val]) => val !== '' && val !== null && val !== undefined),
  );
  const filteredBaseParams = Object.fromEntries(Object.entries(baseParams).filter(([_, val]) => val));
  const paramsData = Object.assign(filteredBaseParams, content, categories, receivers);

  axios
    .post(url, serializeFormData(paramsData), { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } })
    .then((response: any) => {
      if (close) window.location.href = '/apisadmin/' + props.section;
      toast.success({
        title: t('apis/admin/saved'),
        body: t('apis/admin/successfullySaved'),
      });
      setSidebar();
    })
    .catch((error: any) => {
      ModalEventBus.$emit('showModal', { type: 'error', message: error.response ? error.response.data : error });
    });
};

/**
 * Erstellt eine neue Information und blockiert die Nummer
 */
const createNew = (): void => {
  const url = '/apisadmin/create/' + props.section + '/' + newNumber.value;

  axios
    .post(url)
    .then((response: any) => {
      window.location.href = '/apisadmin/edit/' + props.section + '/' + newNumber.value;
    })
    .catch((error: any) => {
      ModalEventBus.$emit('showModal', { type: 'error', message: error.response ? error.response.data : error });
    });
};

/**
 * Lädt eine Information zum Editieren
 *
 * @param {number} number
 */
const load = (number: number) => {
  const url = '/apisadmin/load/edit/' + props.section + '/' + number;

  axios
    .get(url)
    .then((response: any) => {
      informationBuilder.value = response.data.informationBuilder;

      $number.value = parseInt(informationBuilder.value.information.number);

      const titleGerman = informationBuilder.value.information.contentComplete['ger_title'];
      const contentGerman = informationBuilder.value.information.contentComplete['ger_content'];
      contentBlocks.value.ger_title = titleGerman ? titleGerman : '';
      contentBlocks.value.ger_content = contentGerman ? contentGerman : '';

      const titleEnglish = informationBuilder.value.information.contentComplete['eng_title'];
      const contentEnglish = informationBuilder.value.information.contentComplete['eng_content'];
      contentBlocks.value.eng_title = titleEnglish ? titleEnglish : '';
      contentBlocks.value.eng_content = contentEnglish ? contentEnglish : '';
      contentBlocks.value.eng_translate = informationBuilder.value.information.contentComplete['eng_translate'];
      cardEngClosed.value = isEmpty(contentBlocks.value.eng_title) && isEmpty(contentBlocks.value.eng_content);

      const titleFrench = informationBuilder.value.information.contentComplete['fre_title'];
      const contentFrench = informationBuilder.value.information.contentComplete['fre_content'];
      contentBlocks.value.fre_title = titleFrench ? titleFrench : '';
      contentBlocks.value.fre_content = contentFrench ? contentFrench : '';
      contentBlocks.value.fre_translate = informationBuilder.value.information.contentComplete['fre_translate'];
      cardFreClosed.value = isEmpty(contentBlocks.value.fre_title) && isEmpty(contentBlocks.value.fre_content);

      const titleSpanish = informationBuilder.value.information.contentComplete['esl_title'];
      const contentSpanish = informationBuilder.value.information.contentComplete['esl_content'];
      contentBlocks.value.esl_title = titleSpanish ? titleSpanish : '';
      contentBlocks.value.esl_content = contentSpanish ? contentSpanish : '';
      contentBlocks.value.esl_translate = informationBuilder.value.information.contentComplete['esl_translate'];
      cardEslClosed.value = isEmpty(contentBlocks.value.esl_title) && isEmpty(contentBlocks.value.esl_content);

      let attachmentsAndImages = informationBuilder.value.files.concat(informationBuilder.value.images);

      for (let key in attachmentsAndImages) {
        if (attachmentsAndImages[key].language.indexOf('ger') !== -1 || attachmentsAndImages[key].language === '') {
          attachmentsGer.value[attachmentsAndImages[key]['name']] = attachmentsAndImages[key];
        }

        if (attachmentsAndImages[key].language.indexOf('eng') !== -1 || attachmentsAndImages[key].language === '') {
          attachmentsEng.value[attachmentsAndImages[key]['name']] = attachmentsAndImages[key];
        }

        if (attachmentsAndImages[key].language.indexOf('fre') !== -1 || attachmentsAndImages[key].language === '') {
          attachmentsFre.value[attachmentsAndImages[key]['name']] = attachmentsAndImages[key];
        }

        if (attachmentsAndImages[key].language.indexOf('esl') !== -1 || attachmentsAndImages[key].language === '') {
          attachmentsEsl.value[attachmentsAndImages[key]['name']] = attachmentsAndImages[key];
        }
      }

      for (let key in informationBuilder.value.complements) {
        if (informationBuilder.value.complements.hasOwnProperty(key)) {
          references.value[informationBuilder.value.complements[key].number] = {
            number: informationBuilder.value.complements[key].number,
            name: informationBuilder.value.complements[key].title,
            reason_ger: informationBuilder.value.complements[key].reason_ger,
            reason_eng: informationBuilder.value.complements[key].reason_eng,
            reason_fre: informationBuilder.value.complements[key].reason_fre,
            reason_esl: informationBuilder.value.complements[key].reason_esl,
            replaces: false,
          };
        }
      }

      for (let key in informationBuilder.value.replaces) {
        if (informationBuilder.value.replaces.hasOwnProperty(key)) {
          references.value[informationBuilder.value.replaces[key].number] = {
            number: informationBuilder.value.replaces[key].number,
            name: informationBuilder.value.replaces[key].title,
            reason_ger: informationBuilder.value.replaces[key].reason_ger,
            reason_eng: informationBuilder.value.replaces[key].reason_eng,
            reason_fre: informationBuilder.value.replaces[key].reason_fre,
            reason_esl: informationBuilder.value.replaces[key].reason_esl,
            replaces: true,
          };
        }
      }

      selectedCategories.value =
        informationBuilder.value.information.categories !== null ? informationBuilder.value.information.categories : {};
      selectedReceivers.value =
        informationBuilder.value.information.receivers !== null ? informationBuilder.value.information.receivers : {};

      author.value = informationBuilder.value.information.author;
      authorEmail.value = informationBuilder.value.information.authorEmail;
      authorPhone.value = informationBuilder.value.information.authorPhone;

      releaseDate.value =
        informationBuilder.value.information.releaseDate !== null
          ? formatDate(informationBuilder.value.information.releaseDate * 1000)
          : '';
      releaseDateNormalized.value = releaseDate.value ? formatDate(releaseDate.value) : '';

      const sendMailValue = informationBuilder.value.information.sendMail;
      sendMail.value = typeof sendMailValue === 'number' ? sendMailValue === 1 : sendMailValue;
      memo.value = informationBuilder.value.information.memo;
      workflow.value = informationBuilder.value.information.workflow;

      setSidebar();
      dataLoaded.value = true;
    })
    .catch((error: any) => {
      ModalEventBus.$emit('showModal', { type: 'error', message: error.response ? error.response.data : error });
    });
};

const setSidebar = (): void => {
  let requestTranslationButton: string = '';
  let requestPublishingButton: string = '';
  let publishButton: string = '';
  let rejectButton: string = '';

  if (canTransit()) {
    // Buttons für Draft setzen
    if (workflow.value === 'draft') {
      // Eine "Bitte diesen textblock übersetzen" Checkbox geklickt
      if (contentBlocks.value.eng_translate || contentBlocks.value.fre_translate || contentBlocks.value.esl_translate) {
        requestTranslationButton = 'request_translation';
      } else {
        requestPublishingButton = 'request_publishing';
      }
    }

    // Buttons für wait_for_translation setzen
    if (workflow.value === 'wait_for_translation') {
      requestPublishingButton = 'translate';
      rejectButton = 'translator_rejection';
    }

    // Buttons für wait_for_publishing
    if (workflow.value === 'wait_for_publishing') {
      publishButton = 'publish';
      rejectButton = 'publisher_rejection';
    }
  }

  APISEventBus.$emit('setSidebar', {
    section: props.section,
    showIndexButton: false,
    showBackendIndexButton: true,
    showNewInformationButton: false,

    requestTranslationButton: requestTranslationButton,
    requestPublishingButton: requestPublishingButton,
    publishButton: publishButton,
    rejectButton: rejectButton,
  });
};

const categoriesCount = (): number => {
  let categoriesCount: number = 0;
  for (let key of Object.keys(selectedCategories.value)) {
    if (selectedCategories.value.hasOwnProperty(key) && selectedCategories.value[key] !== false) {
      categoriesCount++;
    }
  }
  return categoriesCount;
};

const receiversCount = (): number => {
  let receiversCount: number = 0;
  for (let key of Object.keys(selectedReceivers.value)) {
    if (selectedReceivers.value.hasOwnProperty(key) && selectedReceivers.value[key] !== false) {
      receiversCount++;
    }
  }
  return receiversCount;
};

const canTransit = (): boolean => {
  return (
    contentBlocks.value.ger_title !== undefined &&
    contentBlocks.value.ger_title !== '' &&
    contentBlocks.value.ger_content !== undefined &&
    contentBlocks.value.ger_content.replace(/<.+?>/g, '') !== '' &&
    categoriesCount() > 0 &&
    receiversCount() > 0 &&
    author.value !== '' &&
    author.value !== null &&
    author.value !== undefined &&
    authorEmail.value !== '' &&
    authorEmail.value !== null &&
    authorEmail.value !== undefined &&
    authorPhone.value !== '' &&
    authorPhone.value !== null &&
    authorPhone.value !== undefined &&
    releaseDate.value !== '' &&
    releaseDate.value !== null &&
    releaseDate.value !== undefined
  );
};

const isEmpty = (value: any): boolean => {
  return value === '' || value === undefined || value === null || value === false;
};

const showReceiverList = (): void => {
  APISEventBus.$emit('showReceiverList', { section: props.section, receivers: selectedReceivers.value });
};

/**
 * Lädt die nächste freie Nummer
 */
const loadNextNumber = (): void => {
  axios
    .get('/apisadmin/nextnumber/' + props.section)
    .then((response: any) => {
      newNumber.value = response.data;
    })
    .catch((error: any) => {
      ModalEventBus.$emit('showModal', { type: 'error', message: error.response ? error.response.data : error });
    });
};

const onSelectReference = (item: any): void => {
  // Prüfen ob es die Referenz schon im Array gibt
  if (references.value[item.number] === undefined) {
    item.reason_ger = '';
    item.reason_eng = '';
    item.reason_fre = '';
    item.reason_esl = '';

    item.replaces = false;

    references.value = {
      ...references.value,
      [item.number]: item,
    };
    saveReferences(item.number);
  }
};

const saveReferences = (key: string): void => {
  if (references.value.hasOwnProperty(key)) {
    const url = '/apisadmin/reference/save/' + props.section + '/' + $number.value;

    let params: any = {
      number: references.value[key].number,
      reason_ger: references.value[key].reason_ger,
      reason_eng: references.value[key].reason_eng,
      reason_fre: references.value[key].reason_fre,
      reason_esl: references.value[key].reason_esl,
      replaces: references.value[key].replaces,
    };

    axios
      .post(url, serializeFormData(params), { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } })
      .then((response: any) => {
        toast.success({
          title: t('apis/admin/saved'),
          body: t('apis/admin/successfullySaved'),
        });
      })
      .catch((error: any) => {
        ModalEventBus.$emit('showModal', { type: 'error', message: error.response ? error.response.data : error });
      });
  }
};

const removeReference = (id: number): void => {
  const url = `/apisadmin/reference/delete/${props.section}/${$number.value}/${references.value[id].number}`;

  axios
    .delete(url)
    .then(() => {
      delete references.value[id];
      references.value = { ...references.value };
      toast.info({
        title: t('apis/admin/saved'),
        text: t('apis/admin/successfullySaved'),
      });
    })
    .catch((error: any) => {
      ModalEventBus.$emit('showModal', { type: 'error', message: error.response ? error.response.data : error });
    });
};

const changeState = (transition: string): void => {
  const url = '/apisadmin/state/change/' + props.section + '/' + $number.value + '/' + transition;

  axios
    .post(url)
    .then((response: any) => {
      window.location.href = '/apisadmin/' + props.section;
    })
    .catch((error: any) => {
      ModalEventBus.$emit('showModal', { type: 'error', message: error.response ? error.response.data : error });
    });
};

const setTranslation = (event: Event, key: string): void => {
  event.preventDefault();
  event.stopImmediatePropagation();

  // NUR != WICHTIG
  contentBlocks.value[key] != true ? (contentBlocks.value[key] = true) : (contentBlocks.value[key] = false);

  update();
};

// Lifecycle hooks
onMounted(() => {
  setSidebar();

  $number.value = props.number;

  if ($number.value > 0) {
    load($number.value);
  } else {
    loadNextNumber();
  }

  // Event listeners
  // TODO: Replace with v-model:title and v-model:content (https://vuejs.org/guide/components/v-model.html#multiple-v-model-bindings)
  APISEventBus.$on('synchronizeContentBlock', (data: any) => {
    Object.entries(data).forEach((item: string[]) => {
      contentBlocks.value[item[0]] = item[1];
    });
  });

  APISEventBus.$on('save', () => {
    update();
  });

  APISEventBus.$on('transition', (transition: string) => {
    changeState(transition);
  });

  // Automatisch speichern
  if ($number.value > 0 && dataLoaded.value) {
    setInterval(
      () => {
        update();
      },
      1000 * 60 * 5,
    );
  }
});

onUnmounted(() => {
  APISEventBus.$off('synchronizeContentBlock');
  APISEventBus.$off('save');
  APISEventBus.$off('transition');
});
</script>

<style lang="scss" scoped>
.flag {
  width: 20px;
  height: 15px;
  position: relative;
  top: -2px;
  margin-right: 5px;
}

.referenceContainer {
  margin-top: 10px;

  .checkboxContainer {
    margin-top: 10px;
  }
}

.row.store {
  margin-bottom: 20px;

  button {
    float: right;

    i {
      margin-right: 10px;
    }
  }
}
</style>

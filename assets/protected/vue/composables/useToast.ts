import { useToastController } from 'bootstrap-vue-next';
import type { ColorVariant } from 'bootstrap-vue-next';

export interface ToastConfig {
  title?: string;
  text?: string;
  body?: string;
  timeout?: number;
  progressBar?: boolean;
  position?:
    | 'top-start'
    | 'top-center'
    | 'top-end'
    | 'middle-start'
    | 'middle-center'
    | 'middle-end'
    | 'bottom-start'
    | 'bottom-center'
    | 'bottom-end';
  variant?: ColorVariant;
}

/**
 * Toast type mapping to Bootstrap variants
 */
const TOAST_TYPE_VARIANTS: Record<string, ColorVariant> = {
  success: 'success',
  error: 'danger',
  warning: 'warning',
  info: 'info',
  notify: 'info', // 'notify' maps to 'info' to maintain compatibility
};

/**
 * Default toast configuration
 */
const DEFAULT_CONFIG: ToastConfig = {
  timeout: 2000,
  progressBar: false,
  position: 'bottom-end',
};

export function useToast() {
  const { create } = useToastController();

  /**
   * Shows a toast notification
   * @param type - Toast type (success, error, warning, info, notify)
   * @param config - Toast configuration
   */
  const showToast = (type: string, config: ToastConfig) => {
    const mergedConfig = { ...DEFAULT_CONFIG, ...config };

    // Use text or body for the message content
    const body = mergedConfig.text || mergedConfig.body || '';

    // Determine variant from type or use provided variant
    const variant = mergedConfig.variant || TOAST_TYPE_VARIANTS[type] || 'info';

    // Convert timeout to auto-dismiss value (Bootstrap Vue Next expects milliseconds)
    const autoHideDelay = mergedConfig.timeout || DEFAULT_CONFIG.timeout;

    return create({
      title: mergedConfig.title,
      body,
      variant,
      value: autoHideDelay, // Auto-dismiss after specified time
      position: mergedConfig.position || DEFAULT_CONFIG.position,
      progressProps: mergedConfig.progressBar ? { variant: 'primary' } : undefined,
      noProgress: !mergedConfig.progressBar,
    });
  };

  /**
   * Shows a success toast
   */
  const success = (config: ToastConfig) => showToast('success', config);

  /**
   * Shows an error toast
   */
  const error = (config: ToastConfig) => showToast('error', config);

  /**
   * Shows a warning toast
   */
  const warning = (config: ToastConfig) => showToast('warning', config);

  /**
   * Shows an info toast
   */
  const info = (config: ToastConfig) => showToast('info', config);

  /**
   * Shows a notify toast (alias for info to maintain compatibility)
   */
  const notify = (config: ToastConfig) => showToast('notify', config);

  return {
    showToast,
    success,
    error,
    warning,
    info,
    notify,
  };
}

import { useToastController } from 'bootstrap-vue-next';
import type { ColorVariant } from 'bootstrap-vue-next';

/**
 * Bootstrap Vue Next toast position type
 */
export type ToastPosition =
  | 'top-start'
  | 'top-center'
  | 'top-end'
  | 'middle-start'
  | 'middle-center'
  | 'middle-end'
  | 'bottom-start'
  | 'bottom-center'
  | 'bottom-end';

/**
 * Bootstrap Vue Next native toast configuration
 */
export interface ToastOptions {
  title?: string;
  body?: string;
  variant?: ColorVariant;
  value?: number | boolean; // Auto-dismiss delay in ms, or false to disable
  position?: ToastPosition;
  progressProps?: { variant?: ColorVariant };
  noProgress?: boolean;
  solid?: boolean;
  noCloseButton?: boolean;
  noAutoHide?: boolean;
}

/**
 * Vue 3 composable for Bootstrap Vue Next toast notifications
 * Uses the native Bootstrap Vue Next API
 */
export function useToast() {
  const { create } = useToastController();

  /**
   * Create a toast with the native Bootstrap Vue Next API
   */
  const show = (options: ToastOptions) => {
    return create(options);
  };

  /**
   * Show a success toast
   */
  const success = (options: Omit<ToastOptions, 'variant'>) => {
    return create({
      variant: 'success',
      value: 3000, // Default 3 seconds
      position: 'bottom-end',
      ...options,
    });
  };

  /**
   * Show an error toast
   */
  const error = (options: Omit<ToastOptions, 'variant'>) => {
    return create({
      variant: 'danger',
      value: 5000, // Longer for errors
      position: 'bottom-end',
      ...options,
    });
  };

  /**
   * Show a warning toast
   */
  const warning = (options: Omit<ToastOptions, 'variant'>) => {
    return create({
      variant: 'warning',
      value: 4000,
      position: 'bottom-end',
      ...options,
    });
  };

  /**
   * Show an info toast
   */
  const info = (options: Omit<ToastOptions, 'variant'>) => {
    return create({
      variant: 'info',
      value: 3000,
      position: 'bottom-end',
      ...options,
    });
  };

  return {
    show,
    success,
    error,
    warning,
    info,
  };
}

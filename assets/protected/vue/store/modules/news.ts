import Vue from 'vue';

import Axios from 'axios';

import { store } from '../../store';

import { useToastController } from 'bootstrap-vue-next';

import VueI18n from 'vue-i18n';
Vue.use(VueI18n);

import de from 'Translations/across/across.de_DE.json';
import en from 'Translations/across/across.en_GB.json';
import fr from 'Translations/across/across.fr_FR.json';
import es from 'Translations/across/across.es_ES.json';

const i18n = new VueI18n({
  locale: <string>document.getElementsByTagName('html')[0].getAttribute('lang'),
  messages: { de, en, fr, es },
});

const state = {
  isLoaded: false,
  id: false,
  draft: true,
  content: {
    de_DE: { title: '', content: '' },
    en_GB: { title: '', content: '' },
    fr_FR: { title: '', content: '' },
    es_ES: { title: '', content: '' },
  },
  attachments: [],
  modules: {},
  groups: {},
  halfGroupsCount: 0,
  selectedModules: {},
  selectedGroups: {},
  sendInformationMail: false,
  publishdate: '',
};

const mutations = {
  load: (state: any, payload: any) => {
    Axios.get('/news/modules')
      .then((response: any) => {
        state.modules = response.data;
      })
      .catch((error: any) => {
        store.dispatch('modal/show', { cssClass: 'error', message: error.response.data });
      });

    Axios.get('/news/groups')
      .then((response: any) => {
        state.groups = response.data;
        state.halfGroupsCount = Math.ceil(Object.keys(state.groups).length / 2);
      })
      .catch((error: any) => {
        store.dispatch('modal/show', { cssClass: 'error', message: error.response.data });
      });

    // News laden
    if (payload.edit > -1) {
      Axios.get('/news/load/' + payload.edit)
        .then((response: any) => {
          state.id = payload.edit;
          state.draft = response.data.news.draft;
          state.content = response.data.news.content;
          state.publishdate = response.data.news.publishdate;
          state.attachments = response.data.files;
          state.selectedModules = response.data.news.modules;
          state.sendInformationMail = response.data.sendInformationMail || false;

          for (const i in response.data.news.groups) {
            Vue.set(state.selectedGroups, response.data.news.groups[i], response.data.news.groups[i]);
          }

          state.isLoaded = true;
        })
        .catch((error: any) => {
          store.dispatch('modal/show', { cssClass: 'error', message: error.response.data });
        });
    } else {
      state.isLoaded = true;
      save(state);
    }

    store.watch(
      () => store.getters['modal/click'],
      (value) => {
        if (value === 'delete') {
          removalApproved();
        }
      },
    );
  },
  setDraft: (state: any, payload: boolean) => {
    state.draft = payload;
    save(state);
  },
  setTitleAndContent: (state: any, payload: any) => {
    state.content[payload.lang]['title'] = payload?.title;
    state.content[payload.lang]['content'] = payload?.content;
    save(state);
  },
  setSelectedModules: (state: any, payload: any) => {
    // console.log(state);
    // console.log(payload.uniqueIdentifier);
    // console.log(state.selectedModules[payload.uniqueIdentifier]);

    //Leider funktioniert das. Fragile Magic ... Don't touch or change anything
    if (
      state.selectedModules[payload.uniqueIdentifier] === undefined ||
      state.selectedModules[payload.uniqueIdentifier]
    ) {
      Vue.set(state.selectedModules, payload.uniqueIdentifier, {
        name: payload.name,
        parentUniqueIdentifier: payload.parentUniqueIdentifier,
        parentName: payload.parentName,
      });
    } else {
      Vue.delete(state.selectedModules, payload.uniqueIdentifier);
    }
    save(state);
  },
  setSelectedGroups: (state: any, payload: any) => {
    Vue.set(state.selectedGroups, payload.name, payload.value);
    save(state);
  },
  setPublishDate: (state: any, payload: any) => {
    state.publishdate = payload;
    save(state);
  },
  setSendInformationMail: (state: any, payload: any) => {
    state.sendInformationMail = payload;
    save(state);
  },
  addAttachment: (state: any, payload: any) => {
    payload.file.mimetype.indexOf('image/') === 0 ? (payload.file.type = 'image') : (payload.file.type = 'attachment');
    state.attachments.push(payload.file);
  },
  addGlobalAttachment: (state: any, payload: any) => {
    Axios.patch('/news/global/attachment/' + state.id + '/' + payload.name)
      .then((response: any) => {
        state.attachments.filter(function (attachment: any): void {
          if (attachment['name'] === payload.name) {
            attachment.language = response.data.language;
          }
        });
      })
      .catch((error: any) => {
        store.dispatch('modal/show', { cssClass: 'error', message: error.response.data });
      });
  },
  removeAttachment: (state: any, payload: any) => {
    Axios.delete('/news/delete/attachment/' + state.id + '/' + payload.name)
      .then((response: any) => {
        for (const i in state.attachments) {
          if (state.attachments[i]['name'] === response.data.name) {
            Vue.delete(state.attachments, i);
          }
        }
      })
      .catch((error: any) => {
        store.dispatch('modal/show', { cssClass: 'error', message: error.response.data });
      });
  },
  flipImageAttachment: (state: any, payload: any) => {
    Axios.post('/news/flip/attachment/' + state.id + '/' + payload.name)
      .then((response: any) => {
        state.attachments.filter(function (attachment: any): void {
          if (attachment['name'] === payload.name) {
            attachment.type = response.data.type;
          }
        });
      })
      .catch((error: any) => {
        store.dispatch('modal/show', { cssClass: 'error', message: error.response.data });
      });
  },
  save: (state: any, payload: any) => {
    save(state);
  },
  remove: (state: any, payload: any) => {
    store.dispatch('modal/show', {
      cssClass: 'danger',
      title: i18n.t('frame/news/remove'),
      message: i18n.t('frame/news/remove_message'),
      buttons: [
        {
          title: i18n.t('modal/delete'),
          variant: 'danger',
          clickIdentifier: 'delete',
        },
      ],
    });
  },
  close: (state: any, payload: any) => {
    save(state, true);
    window.location.href = '/news';
  },
};

const actions = {
  load: ({ commit }: any, payload: any) => {
    commit('load', payload);
  },
  setDraft: ({ commit }: any, payload: boolean) => {
    commit('setDraft', payload);
  },
  setTitleAndContent: ({ commit }: any, payload: any) => {
    commit('setTitleAndContent', payload);
  },
  setSelectedModules: ({ commit }: any, payload: any) => {
    commit('setSelectedModules', payload);
  },
  setSelectedGroups: ({ commit }: any, payload: any) => {
    commit('setSelectedGroups', payload);
  },
  setPublishDate: ({ commit }: any, payload: any) => {
    commit('setPublishDate', payload);
  },
  setSendInformationMail: ({ commit }: any, payload: any) => {
    commit('setSendInformationMail', payload);
  },
  addAttachment: ({ commit }: any, payload: any) => {
    commit('addAttachment', payload);
  },
  addGlobalAttachment: ({ commit }: any, payload: any) => {
    commit('addGlobalAttachment', payload);
  },
  removeAttachment: ({ commit }: any, payload: any) => {
    commit('removeAttachment', payload);
  },
  flipImageAttachment: ({ commit }: any, payload: any) => {
    commit('flipImageAttachment', payload);
  },
  save: ({ commit }: any, payload: any) => {
    commit('save', payload);
  },
  remove: ({ commit }: any, payload: any) => {
    commit('remove', payload);
  },
  close: ({ commit }: any, payload: any) => {
    commit('close', payload);
  },
};

const getters = {
  isLoaded: (state: any) => {
    return state.isLoaded;
  },
  draft: (state: any) => {
    return state.draft;
  },
  getId: (state: any) => {
    return state.id;
  },
  getModules: (state: any) => {
    return state.modules;
  },
  getSelectedModules: (state: any) => {
    return state.selectedModules;
  },
  getGroups: (state: any) => {
    return state.groups;
  },
  halfGroupsCount: (state: any) => {
    return state.halfGroupsCount;
  },
  getSelectedGroups: (state: any) => {
    return state.selectedGroups;
  },
  getContent: (state: any) => {
    return state.content;
  },
  getAttachments: (state: any) => (lang: string) => {
    return state.attachments.filter(function (attachment: any): boolean {
      return attachment['language'].indexOf(lang) !== -1;
    });
  },
  hasContent: (state: any) => (lang: string) => {
    return state.content[lang].title !== '' || state.content[lang].content !== '';
  },
  getPublishdate: (state: any) => {
    return state.publishdate;
  },
  getSendInformationMail: (state: any) => {
    return state.sendInformationMail;
  },
};

const namespaced = true;

function save(state: any, closeTriggered = false) {
  Axios.post(
    '/news/save',
    new URLSearchParams({
      'state[id]': state.id,
      'state[draft]': state.draft,
      'state[content]': state.content,
      'state[modules]': state.selectedModules,
      'state[groups]': state.selectedGroups,
      'state[publishdate]': state.publishdate,
      'state[sendInformationMail]': state.sendInformationMail,
      'state[closeTriggered]': closeTriggered,
    }),
  )
    .then((response: any) => {
      if (response.data !== '') state.id = response.data;

      const { create } = useToastController();
      create({
        title: i18n.t('apis/admin/saved'),
        body: i18n.t('apis/admin/successfullySaved'),
        variant: 'info',
        value: 5000,
        position: 'bottom-end',
        noProgress: true,
      });
    })
    .catch((error: any) => {
      store.dispatch('modal/show', { cssClass: 'error', message: error.response.data });
    });
}

function removalApproved() {
  Axios.delete('/news/remove/' + state.id)
    .then((response: any) => {
      window.location.href = '/news';
    })
    .catch((error: any) => {
      store.dispatch('modal/show', { cssClass: 'error', message: error.response.data });
    });
}

export default {
  state,
  mutations,
  actions,

  getters,
  namespaced,
};
